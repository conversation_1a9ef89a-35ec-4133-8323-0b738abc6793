import type { APIRoute } from 'astro';
import { getOrderById, createPaymentTransaction, getOrderPaymentTransactions } from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';

export const prerender = false;

// PhonePe API configuration (use environment variables)
const getPhonePeConfig = (env: any) => ({
  PHONEPE_API_URL: env.PHONEPE_API_URL || "https://api-preprod.phonepe.com/apis/pg-sandbox",
  MERCHANT_ID: env.PHONEPE_MERCHANT_ID || "MERCHANTUAT",
  SALT_KEY: env.PHONEPE_SALT_KEY || "099eb0cd-02cf-4e2a-8aca-3e6c6aff0399",
  SALT_INDEX: env.PHONEPE_SALT_INDEX || "1"
});

/**
 * Initiate a payment for an order using PhonePe
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Get PhonePe configuration
    const config = getPhonePeConfig(locals.runtime.env);

    // Authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }

    const user = (authResult as any).user;
    
    // Parse request body
    const { order_id } = await request.json() as { order_id: number };
    
    if (!order_id) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify order exists and belongs to the user
    const order = await getOrderById(locals.runtime.env, order_id, user.id);
    
    if (!order) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Order not found" 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment method
    if (order.payment_method !== 'online') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "This order doesn't require online payment" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Verify payment status (should be pending)
    if (order.payment_status !== 'pending') {
      return new Response(JSON.stringify({ 
        success: false, 
        message: `Payment already ${order.payment_status}` 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check for existing transactions
    const transactions = await getOrderPaymentTransactions(locals.runtime.env, order.id);
    const pendingTransaction = transactions.find(tx => 
      tx.status === 'initiated' || tx.status === 'pending');
    
    if (pendingTransaction) {
      // Return existing transaction details
      try {
        const gatewayResponse = JSON.parse(pendingTransaction.gateway_response || '{}');
        return new Response(JSON.stringify({ 
          success: true,
          transaction: pendingTransaction,
          redirectUrl: gatewayResponse.data?.instrumentResponse?.redirectInfo?.url || null,
          message: "Payment already initiated"
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (e) {
        // Handle parse error
        console.error('Error parsing gateway response:', e);
      }
    }
    
    // Generate a unique merchant transaction ID (must be < 35 characters)
    const merchantTxnId = `MT${Date.now()}${Math.floor(Math.random() * 100)}`;
    
    // Get phone number from address
    let phoneNumber = '';
    if (order.address && order.address.phone) {
      phoneNumber = order.address.phone.replace(/[^0-9]/g, '');
    }
    
    // Validate amount (PhonePe requires minimum 100 paise = ₹1)
    const amountInPaise = Math.round(order.total_amount * 100);
    if (amountInPaise < 100) {
      return new Response(JSON.stringify({
        success: false,
        message: "Amount must be at least ₹1"
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create PhonePe request payload
    const phonepePayload = {
      merchantId: config.MERCHANT_ID,
      merchantTransactionId: merchantTxnId,
      merchantUserId: `MU${user.id}`,
      amount: amountInPaise,
      redirectUrl: `${new URL(request.url).origin}/checkout/status?transactionId=${merchantTxnId}`,
      redirectMode: "REDIRECT",
      callbackUrl: `${new URL(request.url).origin}/api/payments/webhook`,
      mobileNumber: phoneNumber || "9999999999", // Fallback mobile number for testing
      paymentInstrument: {
        type: "PAY_PAGE"
      }
    };

    // Log payload for debugging (remove in production)
    console.log('PhonePe Payload:', JSON.stringify(phonepePayload, null, 2));
    console.log('Config being used:', {
      MERCHANT_ID: config.MERCHANT_ID,
      SALT_KEY: config.SALT_KEY,
      SALT_INDEX: config.SALT_INDEX,
      API_URL: config.PHONEPE_API_URL
    });

    // Convert to base64
    const base64Payload = Buffer.from(JSON.stringify(phonepePayload)).toString("base64");
    console.log('Base64 Payload:', base64Payload);

    // Generate checksum (SHA256 hash) for the payload
    const crypto = await import('crypto');
    const string = `${base64Payload}/pg/v1/pay${config.SALT_KEY}`;
    const sha256 = crypto.createHash('sha256').update(string).digest('hex');
    const checksum = `${sha256}###${config.SALT_INDEX}`;

    // Log checksum for debugging (remove in production)
    console.log('Checksum string:', string);
    console.log('SHA256 hash:', sha256);
    console.log('Generated checksum:', checksum);

    // Test with a known working example from PhonePe docs
    const testPayload = {
      merchantId: "MERCHANTUAT",
      merchantTransactionId: "MT7850590068188104",
      merchantUserId: "MUID123",
      amount: 10000,
      redirectUrl: "https://webhook.site/redirect-url",
      redirectMode: "REDIRECT",
      callbackUrl: "https://webhook.site/callback-url",
      mobileNumber: "9999999999",
      paymentInstrument: {
        type: "PAY_PAGE"
      }
    };
    const testBase64 = Buffer.from(JSON.stringify(testPayload)).toString("base64");
    const testString = `${testBase64}/pg/v1/pay${config.SALT_KEY}`;
    const testSha256 = crypto.createHash('sha256').update(testString).digest('hex');
    const testChecksum = `${testSha256}###${config.SALT_INDEX}`;
    console.log('Test checksum for known payload:', testChecksum);

    // Make API call to PhonePe
    const response = await fetch(`${config.PHONEPE_API_URL}/pg/v1/pay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum
      },
      body: JSON.stringify({ request: base64Payload })
    });
    
    // Log response status and headers for debugging
    console.log('PhonePe API Response Status:', response.status);
    console.log('PhonePe API Response Headers:', Object.fromEntries(response.headers.entries()));

    const phonepeResponse: any = await response.json();

    // Log full response for debugging
    console.log('PhonePe API Response:', JSON.stringify(phonepeResponse, null, 2));

    // Check for API errors
    if (!phonepeResponse.success) {
      console.error('PhonePe API error:', phonepeResponse);
      return new Response(JSON.stringify({
        success: false,
        message: phonepeResponse.message || "Payment gateway error",
        code: phonepeResponse.code || "UNKNOWN_ERROR",
        debug: {
          merchantId: config.MERCHANT_ID,
          apiUrl: config.PHONEPE_API_URL,
          payloadLength: base64Payload.length
        }
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Store transaction record in our database
    const transaction = await createPaymentTransaction(locals.runtime.env, {
      order_id: order.id,
      transaction_id: merchantTxnId,
      payment_method: 'phonepe',
      amount: order.total_amount,
      status: 'initiated',
      gateway_response: JSON.stringify(phonepeResponse)
    });
    
    if (!transaction) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: "Failed to store transaction" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return success with redirect URL for the payment page
    return new Response(JSON.stringify({ 
      success: true, 
      transaction: transaction,
      redirectUrl: phonepeResponse.data.instrumentResponse.redirectInfo.url,
      message: "Payment initiated successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error initiating payment:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      message: "Failed to initiate payment" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};