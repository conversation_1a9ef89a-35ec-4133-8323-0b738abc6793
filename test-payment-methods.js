// Test script for category-aware payment methods functionality
// Run this in the browser console on the admin payment methods page

async function testCategoryPaymentMethods() {
  console.log('🚀 Testing Category-Aware Payment Methods...');
  
  try {
    // Test 1: Fetch all categories
    console.log('\n📂 Test 1: Fetching categories...');
    const categoriesResponse = await window.ApiClient.getCategories();
    console.log('Categories response:', categoriesResponse);
    
    if (!categoriesResponse.success) {
      console.error('❌ Failed to fetch categories:', categoriesResponse.error);
      return;
    }
    
    console.log('✅ Categories fetched successfully:', categoriesResponse.categories.length, 'categories');
    
    // Test 2: Fetch payment method settings with categories
    console.log('\n⚙️ Test 2: Fetching payment method settings with categories...');
    const settingsResponse = await window.ApiClient.getPaymentMethodSettingsWithCategories();
    console.log('Settings response:', settingsResponse);
    
    if (!settingsResponse.success) {
      console.error('❌ Failed to fetch payment method settings');
      return;
    }
    
    console.log('✅ Payment method settings fetched successfully');
    
    // Test 3: Set category-specific restrictions (disable online payment for first category)
    if (categoriesResponse.categories.length > 0) {
      const firstCategory = categoriesResponse.categories[0];
      console.log(`\n🔧 Test 3: Setting restrictions for category "${firstCategory.name}" (ID: ${firstCategory.id})`);
      
      const updateResponse = await window.ApiClient.updateCategoryPaymentMethodSettings(
        firstCategory.id,
        {
          online_payment_enabled: false, // Disable online payment
          cash_on_delivery_enabled: true  // Keep COD enabled
        }
      );
      
      console.log('Update response:', updateResponse);
      
      if (updateResponse.success) {
        console.log('✅ Category payment method settings updated successfully');
        
        // Test 4: Verify the settings were applied
        console.log('\n🔍 Test 4: Verifying updated settings...');
        const verifyResponse = await window.ApiClient.getPaymentMethodSettingsWithCategories();
        
        if (verifyResponse.success) {
          const categoryOverride = verifyResponse.categories?.find(cat => cat.category_id === firstCategory.id);
          if (categoryOverride) {
            console.log('✅ Category override found:', categoryOverride);
            if (!categoryOverride.online_payment_enabled && categoryOverride.cash_on_delivery_enabled) {
              console.log('✅ Settings applied correctly: Online payment disabled, COD enabled');
            } else {
              console.log('⚠️ Settings may not have been applied correctly');
            }
          } else {
            console.log('⚠️ No category override found');
          }
        }
        
        // Test 5: Test cart payment methods API with mock cart items
        console.log('\n🛒 Test 5: Testing cart payment methods API...');
        const mockCartItems = [
          {
            product_id: 1, // Assuming this product belongs to the restricted category
            id: 1,
            name: 'Test Product',
            quantity: 1
          }
        ];
        
        const cartPaymentResponse = await window.ApiClient.getCartPaymentMethods(mockCartItems);
        console.log('Cart payment methods response:', cartPaymentResponse);
        
        if (cartPaymentResponse.success) {
          console.log('✅ Cart payment methods API working');
          if (!cartPaymentResponse.settings.online_payment_enabled) {
            console.log('✅ Correct! Online payment disabled for cart with restricted category');
          }
        }
        
        // Test 6: Clean up - remove category restriction
        console.log('\n🧹 Test 6: Cleaning up - removing category restriction...');
        const deleteResponse = await window.ApiClient.deleteCategoryPaymentMethodSettings(firstCategory.id);
        console.log('Delete response:', deleteResponse);
        
        if (deleteResponse.success) {
          console.log('✅ Category restriction removed successfully');
        }
        
      } else {
        console.error('❌ Failed to update category payment method settings:', updateResponse.error);
      }
    }
    
    console.log('\n🎉 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testCategoryPaymentMethods();
