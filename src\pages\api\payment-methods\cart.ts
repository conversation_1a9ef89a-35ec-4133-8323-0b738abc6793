import type { APIRoute } from 'astro';
import { getPaymentMethodSettings, getPaymentMethodSettingsWithCategories, getProductById } from '../../../db/database';

export const prerender = false;

/**
 * Get available payment methods for cart items
 * Considers category-specific payment method restrictions
 */
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Parse request body to get cart items
    const { items } = await request.json();
    
    if (!items || !Array.isArray(items) || items.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Cart items are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get global settings and category-specific overrides
    const settingsResponse = await getPaymentMethodSettingsWithCategories(locals.runtime.env);
    
    if (!settingsResponse) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Failed to fetch payment method settings'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    const globalSettings = settingsResponse.global;
    const categorySettings = settingsResponse.categories || [];

    // Get product details to determine categories
    const productCategories = new Set();
    for (const item of items) {
      try {
        const product = await getProductById(locals.runtime.env, parseInt(item.product_id || item.id));
        if (product && product.category_id) {
          productCategories.add(product.category_id);
        }
      } catch (error) {
        console.error(`Error fetching product ${item.product_id || item.id}:`, error);
        // Continue with other products
      }
    }

    // Determine available payment methods based on all categories in cart
    let onlinePaymentEnabled = globalSettings.online_payment_enabled;
    let cashOnDeliveryEnabled = globalSettings.cash_on_delivery_enabled;

    // Check if any category has disabled payment methods
    for (const categoryId of productCategories) {
      const categoryOverride = categorySettings.find(cs => cs.category_id === categoryId);
      if (categoryOverride) {
        // If any category disables a payment method, it's disabled for the entire cart
        if (!categoryOverride.online_payment_enabled) {
          onlinePaymentEnabled = false;
        }
        if (!categoryOverride.cash_on_delivery_enabled) {
          cashOnDeliveryEnabled = false;
        }
      }
    }

    return new Response(JSON.stringify({
      success: true,
      settings: {
        online_payment_enabled: onlinePaymentEnabled,
        cash_on_delivery_enabled: cashOnDeliveryEnabled
      },
      categories_checked: Array.from(productCategories),
      global_settings: globalSettings,
      category_overrides: categorySettings.filter(cs => productCategories.has(cs.category_id))
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error getting cart payment methods:', error);
    
    // Return fallback settings
    return new Response(JSON.stringify({
      success: true,
      settings: {
        online_payment_enabled: true,
        cash_on_delivery_enabled: true
      },
      error: error.toString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
