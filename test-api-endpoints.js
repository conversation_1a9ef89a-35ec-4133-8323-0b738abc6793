// Test API endpoints for payment methods functionality
const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4321,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testEndpoints() {
  console.log('🧪 Testing Payment Methods API Endpoints...\n');

  try {
    // Test categories endpoint
    console.log('1. Testing Categories API...');
    const categoriesResult = await makeRequest('/api/admin/categories');
    console.log(`   Status: ${categoriesResult.status}`);
    if (categoriesResult.status === 200 && categoriesResult.data.success) {
      console.log(`   ✅ Categories: ${categoriesResult.data.categories.length} found`);
    } else {
      console.log('   ❌ Categories test failed');
    }

    // Test payment methods endpoint (global)
    console.log('\n2. Testing Payment Methods API (global)...');
    const paymentMethodsResult = await makeRequest('/api/admin/payment-methods');
    console.log(`   Status: ${paymentMethodsResult.status}`);
    if (paymentMethodsResult.status === 200 && paymentMethodsResult.data.success) {
      console.log(`   ✅ Payment methods fetched successfully`);
      console.log(`   Global settings: Online Payment: ${paymentMethodsResult.data.settings.onlinePayment}, COD: ${paymentMethodsResult.data.settings.cashOnDelivery}`);
    } else {
      console.log('   ❌ Payment methods test failed');
    }

    // Test payment methods endpoint with categories
    console.log('\n3. Testing Payment Methods API (with categories)...');
    const paymentMethodsWithCategoriesResult = await makeRequest('/api/admin/payment-methods?withCategories=true');
    console.log(`   Status: ${paymentMethodsWithCategoriesResult.status}`);
    if (paymentMethodsWithCategoriesResult.status === 200 && paymentMethodsWithCategoriesResult.data.success) {
      console.log(`   ✅ Payment methods with categories fetched successfully`);
      const categorySettings = paymentMethodsWithCategoriesResult.data.settings.categorySettings || [];
      console.log(`   Category-specific settings: ${categorySettings.length} found`);
    } else {
      console.log('   ❌ Payment methods with categories test failed');
    }

    // Test cart payment methods endpoint (without cart items)
    console.log('\n4. Testing Cart Payment Methods API (empty cart)...');
    const cartPaymentMethodsResult = await makeRequest('/api/payment-methods/cart', 'POST', { cartItems: [] });
    console.log(`   Status: ${cartPaymentMethodsResult.status}`);
    if (cartPaymentMethodsResult.status === 200 && cartPaymentMethodsResult.data.success) {
      console.log(`   ✅ Cart payment methods fetched successfully (empty cart)`);
      console.log(`   Available: Online Payment: ${cartPaymentMethodsResult.data.settings.onlinePayment}, COD: ${cartPaymentMethodsResult.data.settings.cashOnDelivery}`);
    } else {
      console.log('   ❌ Cart payment methods test failed');
    }

    console.log('\n🎉 API endpoint testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testEndpoints();
